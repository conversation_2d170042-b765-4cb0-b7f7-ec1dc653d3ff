#!/usr/bin/env python3
"""
Mac用のセットアップスクリプト
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """Pythonのバージョンをチェック"""
    if sys.version_info < (3, 7):
        print("エラー: Python 3.7以上が必要です。")
        return False
    print(f"Python {sys.version_info.major}.{sys.version_info.minor} を使用しています。")
    return True

def check_ffmpeg():
    """FFmpegがインストールされているかチェック"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ FFmpegがシステムにインストールされています。")
            # バージョン情報を表示
            version_line = result.stdout.split('\n')[0] if result.stdout else "バージョン情報不明"
            print(f"  バージョン: {version_line}")
            return True
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    print("✗ FFmpegがシステムにインストールされていません。")
    return False

def install_ffmpeg():
    """FFmpegをインストール"""
    print("\nFFmpegをインストールします...")
    
    # Homebrewがインストールされているかチェック
    try:
        result = subprocess.run(['brew', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("Homebrewを使用してFFmpegをインストールします...")
            subprocess.run(['brew', 'install', 'ffmpeg'], check=True)
            print("✓ FFmpegのインストールが完了しました。")
            return True
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("Homebrewがインストールされていません。")
    
    print("手動でFFmpegをインストールしてください:")
    print("1. Homebrewをインストール: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
    print("2. FFmpegをインストール: brew install ffmpeg")
    print("または、https://evermeet.cx/ffmpeg/ からダウンロードしてください。")
    return False

def install_requirements():
    """必要なPythonパッケージをインストール"""
    print("\n必要なPythonパッケージをインストールします...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    if requirements_file.exists():
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)], check=True)
            print("✓ パッケージのインストールが完了しました。")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ パッケージのインストールに失敗しました: {e}")
            return False
    else:
        print("requirements.txtが見つかりません。")
        return False

def create_config():
    """設定ファイルを作成"""
    config_file = Path(__file__).parent / "config.json"
    if not config_file.exists():
        config_content = {
            "api_key": "",
            "model": "gemini-2.0-flash",
            "language": "japanese",
            "timestamp": True,
            "autosave": False,
            "minutes": True
        }
        
        import json
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_content, f, indent=2, ensure_ascii=False)
        
        print("✓ 設定ファイルを作成しました。")

def main():
    """メイン関数"""
    print("=== Mac用セットアップスクリプト ===")
    print(f"OS: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version}")
    
    # Pythonバージョンチェック
    if not check_python_version():
        return
    
    # FFmpegチェック
    if not check_ffmpeg():
        install_ffmpeg()
    
    # パッケージインストール
    install_requirements()
    
    # 設定ファイル作成
    create_config()
    
    print("\n=== セットアップ完了 ===")
    print("音声文字起こしツールを実行する準備ができました。")
    print("実行方法: python transcribe_gui.py")

if __name__ == "__main__":
    main() 