#!/usr/bin/env python3
"""
macOS対応GUI版音声文字起こしツール
"""

import os
import sys
import warnings
import tkinter as tk
from tkinter import filedialog, scrolledtext, messagebox, ttk
from pathlib import Path
import threading
import json
import platform

# urllib3の警告を抑制
warnings.filterwarnings("ignore", category=UserWarning, module="urllib3")

# 既存のtranscribe.pyから関数をインポート
from transcribe import load_audio_file, transcribe_audio

# 設定ファイルのパス
CONFIG_FILE = "config.json"

def get_resource_path(relative_path):
    """リソースファイルの絶対パスを取得する"""
    try:
        # PyInstallerでビルドされた場合のパスを取得
        base_path = sys._MEIPASS
    except Exception:
        # 通常のPython実行時は、現在のディレクトリを返す
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def get_working_directory():
    """作業ディレクトリを取得する"""
    if getattr(sys, 'frozen', False):
        # 実行ファイル実行時は、実行ファイルのディレクトリを返す
        return os.path.dirname(os.path.abspath(sys.executable))
    else:
        # 通常のPython実行時は、現在のディレクトリを返す
        return os.path.abspath(os.getcwd())

class TranscribeApp:
    def __init__(self, root):
        self.root = root
        self.root.title("音声文字起こしツール")
        self.root.geometry("800x900")
        self.root.minsize(800, 700)
        
        # macOS用の設定
        if platform.system() == "Darwin":
            # macOS用の設定
            self.root.tk.call('tk', 'scaling', 1.0)  # スケーリングを1.0に設定
        
        # 設定の読み込み
        self.config = self.load_config()
        
        # メインフレーム
        self.main_frame = tk.Frame(root, padx=20, pady=20)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # タイトル
        self.title_label = tk.Label(
            self.main_frame, 
            text="音声文字起こし・議事録作成ツール", 
            pady=10
        )
        self.title_label.pack(fill=tk.X)
        
        # ファイル選択セクション
        self.file_frame = tk.LabelFrame(
            self.main_frame, 
            text="音声ファイルの選択", 
            padx=10, 
            pady=10
        )
        self.file_frame.pack(fill=tk.X, pady=10)
        
        # ファイルパス表示
        self.path_var = tk.StringVar()
        self.path_entry = tk.Entry(
            self.file_frame, 
            textvariable=self.path_var, 
            width=50
        )
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 参照ボタン
        self.browse_button = tk.Button(
            self.file_frame, 
            text="参照...", 
            command=self.browse_file,
            width=10
        )
        self.browse_button.pack(side=tk.RIGHT)
        
        # APIキー設定セクション
        self.api_frame = tk.LabelFrame(
            self.main_frame, 
            text="API設定", 
            padx=10, 
            pady=10
        )
        self.api_frame.pack(fill=tk.X, pady=10)
        
        # APIキー入力
        self.api_label = tk.Label(
            self.api_frame, 
            text="Google API キー:", 
        )
        self.api_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.api_var = tk.StringVar(value=self.config.get("api_key", ""))
        self.api_entry = tk.Entry(
            self.api_frame, 
            textvariable=self.api_var, 
            width=40,
            show="*"  # パスワードのように表示
        )
        self.api_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # APIキー表示切り替えチェックボックス
        self.show_api_var = tk.BooleanVar(value=False)
        self.show_api_check = tk.Checkbutton(
            self.api_frame,
            text="APIキーを表示",
            variable=self.show_api_var,
            command=self.toggle_api_visibility
        )
        self.show_api_check.grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # APIキー保存ボタン
        self.save_api_button = tk.Button(
            self.api_frame,
            text="APIキーを保存",
            command=self.save_api_key,
            width=15
        )
        self.save_api_button.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # オプションフレーム
        self.options_frame = tk.LabelFrame(
            self.main_frame, 
            text="オプション", 
            padx=10, 
            pady=10
        )
        self.options_frame.pack(fill=tk.X, pady=10)
        
        # モデル選択
        self.model_label = tk.Label(
            self.options_frame, 
            text="モデル:", 
        )
        self.model_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.model_var = tk.StringVar(value="gemini-2.0-flash")
        self.model_combo = ttk.Combobox(
            self.options_frame, 
            textvariable=self.model_var, 
            values=["gemini-2.0-flash"],
            state="readonly",
            width=20
        )
        self.model_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 言語選択の追加
        self.language_label = tk.Label(
            self.options_frame, 
            text="言語:", 
        )
        self.language_label.grid(row=0, column=2, sticky=tk.W, padx=(20, 5), pady=5)
        
        self.language_var = tk.StringVar(value="japanese")
        self.language_combo = ttk.Combobox(
            self.options_frame, 
            textvariable=self.language_var, 
            values=["japanese", "english"],
            state="readonly",
            width=10
        )
        self.language_combo.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # タイムスタンプオプション
        self.timestamp_var = tk.BooleanVar(value=True)
        self.timestamp_check = tk.Checkbutton(
            self.options_frame, 
            text="タイムスタンプを付ける", 
            variable=self.timestamp_var,
        )
        self.timestamp_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # 自動保存オプション
        self.autosave_var = tk.BooleanVar(value=False)
        self.autosave_check = tk.Checkbutton(
            self.options_frame, 
            text="結果を自動的にファイルに保存する", 
            variable=self.autosave_var,
        )
        self.autosave_check.grid(row=1, column=2, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # 議事録生成オプション
        self.minutes_var = tk.BooleanVar(value=True)
        self.minutes_check = tk.Checkbutton(
            self.options_frame, 
            text="議事録も生成する", 
            variable=self.minutes_var,
        )
        self.minutes_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # ステータス表示
        self.status_var = tk.StringVar(value="準備完了")
        self.status_label = tk.Label(
            self.main_frame,
            textvariable=self.status_var,
            fg="blue",
            anchor=tk.W
        )
        self.status_label.pack(fill=tk.X, pady=(0, 5))
        
        # 実行ボタン
        self.execute_button = tk.Button(
            self.main_frame, 
            text="文字起こしを実行", 
            bg="#4CAF50", 
            fg="white",
            command=self.execute_transcription,
            height=2
        )
        self.execute_button.pack(fill=tk.X, pady=10)
        
        # 進捗バー
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.main_frame, 
            orient=tk.HORIZONTAL, 
            length=100, 
            mode='indeterminate', 
            variable=self.progress_var
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # 結果表示ペイン
        self.button_frame = tk.Frame(self.main_frame)
        self.button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 10))
        
        # 文字起こし結果保存ボタン
        self.save_button = tk.Button(
            self.button_frame,
            text="文字起こし結果を保存",
            command=self.save_result,
            state=tk.DISABLED,
            width=20
        )
        self.save_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 議事録保存ボタン
        self.save_minutes_button = tk.Button(
            self.button_frame,
            text="議事録を保存",
            command=self.save_minutes,
            state=tk.DISABLED,
            width=20
        )
        self.save_minutes_button.pack(side=tk.LEFT)
        
        # PanedWindowの高さを制限して、ボタンが見えるようにする
        results_frame = tk.Frame(self.main_frame)
        results_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 5))
        
        self.results_paned = ttk.PanedWindow(results_frame, orient=tk.VERTICAL)
        self.results_paned.pack(fill=tk.BOTH, expand=True)
        
        # 文字起こし結果フレーム
        self.transcription_frame = tk.LabelFrame(
            self.results_paned,
            text="文字起こし結果",
            padx=10,
            pady=10
        )
        
        # 議事録フレーム
        self.minutes_frame = tk.LabelFrame(
            self.results_paned,
            text="議事録",
            padx=10,
            pady=10
        )
        
        # ペインに追加
        self.results_paned.add(self.transcription_frame, weight=1)
        self.results_paned.add(self.minutes_frame, weight=1)
        
        # 文字起こし結果テキストエリア
        self.result_text = scrolledtext.ScrolledText(
            self.transcription_frame,
            wrap=tk.WORD,
            height=20
        )
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        # 議事録テキストエリア
        self.minutes_text = scrolledtext.ScrolledText(
            self.minutes_frame,
            wrap=tk.WORD,
            height=20
        )
        self.minutes_text.pack(fill=tk.BOTH, expand=True)
        
        # 初期状態ではプログレスバーを非表示に
        self.progress_bar.pack_forget()
        
        # 現在の処理状態
        self.processing = False
        
        # 現在の文字起こし結果と議事録
        self.current_result = ""
        self.current_minutes = ""

    def browse_file(self):
        """ファイル選択ダイアログを表示して音声ファイルを選択する"""
        filetypes = [
            ("すべての音声ファイル", "*.wav *.flac *.mp3 *.ogg *.webm *.mp4 *.amr *.3gp *.m4a *.opus *.speex"),
            ("WAVファイル", "*.wav"),
            ("FLACファイル", "*.flac"),
            ("MP3ファイル", "*.mp3"),
            ("OGGファイル", "*.ogg"),
            ("すべてのファイル", "*.*")
        ]
        
        try:
            filepath = filedialog.askopenfilename(
                title="音声ファイルを選択",
                filetypes=filetypes,
                initialdir=None
            )
            
            if filepath:
                abs_path = os.path.abspath(filepath)
                if not os.path.exists(abs_path):
                    error_msg = f"ファイルが見つかりません: {abs_path}"
                    messagebox.showerror("エラー", error_msg)
                    return
                
                self.path_var.set(abs_path)
                
        except Exception as e:
            error_msg = f"ファイル選択中にエラーが発生しました:\n{str(e)}"
            messagebox.showerror("エラー", error_msg)
    
    def execute_transcription(self):
        """文字起こし処理を実行する"""
        filepath = self.path_var.get().strip()
        
        if not filepath:
            messagebox.showerror("エラー", "音声ファイルを選択してください。")
            return
        
        filepath = os.path.abspath(filepath)
        
        if not os.path.exists(filepath):
            messagebox.showerror("エラー", f"ファイルが見つかりません: {filepath}")
            return
        
        if self.processing:
            return
        
        api_key = self.api_var.get().strip()
        if not api_key:
            messagebox.showerror("エラー", "APIキーが設定されていません。")
            return
        
        os.environ["GOOGLE_API_KEY"] = api_key
        
        self.processing = True
        self.execute_button.config(state=tk.DISABLED, text="処理中...")
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        self.progress_bar.start(10)
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "文字起こし処理中です。しばらくお待ちください...")
        self.minutes_text.delete(1.0, tk.END)
        if self.minutes_var.get():
            self.minutes_text.insert(tk.END, "議事録を生成中です。しばらくお待ちください...")
        self.save_button.config(state=tk.DISABLED)
        self.save_minutes_button.config(state=tk.DISABLED)
        
        thread = threading.Thread(target=self.process_transcription, args=(filepath,))
        thread.daemon = True
        thread.start()
    
    def process_transcription(self, filepath):
        """バックグラウンドで文字起こし処理を実行する"""
        try:
            api_key = self.api_var.get().strip()
            if not api_key:
                self.update_result("エラー: APIキーが設定されていません。API設定セクションでAPIキーを入力してください。", is_error=True)
                self.finish_processing()
                return
            
            os.environ["GOOGLE_API_KEY"] = api_key
            
            model = self.model_var.get()
            language = self.language_var.get()
            with_timestamps = self.timestamp_var.get()
            generate_minutes = self.minutes_var.get()
            
            self.update_status("音声ファイルを読み込み中...")
            self.update_result("音声ファイルを読み込み中です。しばらくお待ちください...")
            if generate_minutes:
                self.update_minutes("議事録を準備中です...")
            
            try:
                audio, format_name = load_audio_file(filepath)
            except Exception as e:
                error_msg = f"音声ファイルの読み込みに失敗しました:\n{str(e)}"
                self.update_result(error_msg, True)
                self.finish_processing()
                return
            
            duration_minutes = len(audio) / (1000 * 60)
            
            timestamp_status = "タイムスタンプあり" if with_timestamps else "タイムスタンプなし"
            minutes_status = "議事録生成あり" if generate_minutes else "議事録生成なし"
            
            status_message = f"音声の長さ: {duration_minutes:.1f}分、処理を開始します..."
            self.update_status(status_message)
            
            result_info = (f"音声ファイル: {os.path.basename(filepath)}\n"
                          f"長さ: {duration_minutes:.1f}分\n"
                          f"言語: {language}\n"
                          f"{timestamp_status}\n"
                          f"{minutes_status}\n"
                          f"モデル: {model}\n\n"
                          f"処理を開始します...\n")
            
            self.update_result(result_info)
            
            if generate_minutes:
                self.update_minutes("文字起こし完了後に議事録を生成します...\n" + result_info)
            
            if generate_minutes:
                transcription, minutes = transcribe_audio(
                    audio, 
                    model_name=model, 
                    language=language, 
                    with_timestamps=with_timestamps,
                    generate_minutes_flag=True
                )
                self.current_minutes = minutes
                self.root.after(0, self.update_minutes, minutes)
            else:
                transcription = transcribe_audio(
                    audio, 
                    model_name=model, 
                    language=language, 
                    with_timestamps=with_timestamps
                )
            
            if self.autosave_var.get():
                output_filepath = Path(filepath).with_suffix('.txt')
                with open(output_filepath, "w", encoding="utf-8") as f:
                    f.write(transcription)
                
                output_message = f"\n\n[文字起こし結果をファイルに保存しました: {output_filepath}]"
                transcription += output_message
                
                if generate_minutes:
                    minutes_filepath = Path(filepath).stem + "_minutes.md"
                    minutes_filepath = Path(filepath).parent / minutes_filepath
                    with open(minutes_filepath, "w", encoding="utf-8") as f:
                        f.write(self.current_minutes)
                    
                    minutes_message = f"\n\n[議事録をファイルに保存しました: {minutes_filepath}]"
                    self.current_minutes += minutes_message
                    self.root.after(0, self.update_minutes, self.current_minutes)
            
            self.current_result = transcription
            self.root.after(0, self.update_result, transcription)
            self.update_status("処理完了")
            
        except Exception as e:
            error_message = f"エラーが発生しました: {str(e)}"
            self.root.after(0, self.update_result, error_message, True)
            if self.minutes_var.get():
                self.root.after(0, self.update_minutes, "議事録の生成中にエラーが発生しました。", True)
            self.update_status("エラーが発生しました")
        finally:
            self.root.after(0, self.finish_processing)
    
    def update_status(self, message):
        """ステータスメッセージを更新する（スレッドセーフ）"""
        self.root.after(0, lambda: self.status_var.set(message))
    
    def update_result(self, text, is_error=False):
        """UIスレッドで文字起こし結果表示を更新する"""
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, text)
        
        if is_error:
            self.result_text.tag_configure("error", foreground="red")
            self.result_text.tag_add("error", "1.0", tk.END)
    
    def update_minutes(self, text, is_error=False):
        """UIスレッドで議事録表示を更新する"""
        self.minutes_text.delete(1.0, tk.END)
        self.minutes_text.insert(tk.END, text)
        
        if is_error:
            self.minutes_text.tag_configure("error", foreground="red")
            self.minutes_text.tag_add("error", "1.0", tk.END)
    
    def finish_processing(self):
        """処理完了後にUIを元に戻す"""
        self.processing = False
        self.execute_button.config(state=tk.NORMAL, text="文字起こしを実行")
        self.progress_bar.stop()
        self.progress_bar.pack_forget()
        
        if self.current_result:
            self.save_button.config(state=tk.NORMAL)
        
        if self.current_minutes:
            self.save_minutes_button.config(state=tk.NORMAL)
    
    def save_result(self):
        """文字起こし結果をファイルに保存する"""
        if not self.current_result:
            return
        
        filepath = filedialog.asksaveasfilename(
            title="文字起こし結果を保存",
            defaultextension=".txt",
            filetypes=[("テキストファイル", "*.txt"), ("すべてのファイル", "*.*")]
        )
        
        if filepath:
            try:
                with open(filepath, "w", encoding="utf-8") as f:
                    f.write(self.current_result)
                messagebox.showinfo("保存完了", f"文字起こし結果を保存しました: {filepath}")
            except Exception as e:
                messagebox.showerror("エラー", f"保存中にエラーが発生しました: {str(e)}")
    
    def save_minutes(self):
        """議事録をファイルに保存する"""
        if not self.current_minutes:
            return
        
        filepath = filedialog.asksaveasfilename(
            title="議事録を保存",
            defaultextension=".md",
            filetypes=[("マークダウンファイル", "*.md"), ("テキストファイル", "*.txt"), ("すべてのファイル", "*.*")]
        )
        
        if filepath:
            try:
                with open(filepath, "w", encoding="utf-8") as f:
                    f.write(self.current_minutes)
                messagebox.showinfo("保存完了", f"議事録を保存しました: {filepath}")
            except Exception as e:
                messagebox.showerror("エラー", f"保存中にエラーが発生しました: {str(e)}")

    def toggle_api_visibility(self):
        """APIキーの表示/非表示を切り替える"""
        if self.show_api_var.get():
            self.api_entry.config(show="")
        else:
            self.api_entry.config(show="*")
    
    def save_api_key(self):
        """APIキーを保存する"""
        api_key = self.api_var.get().strip()
        if not api_key:
            messagebox.showerror("エラー", "APIキーが入力されていません。")
            return
        
        self.config["api_key"] = api_key
        self.save_config()
        os.environ["GOOGLE_API_KEY"] = api_key
        messagebox.showinfo("成功", "APIキーが保存されました。")
    
    def load_config(self):
        """設定ファイルを読み込む"""
        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, "r") as f:
                    return json.load(f)
            except Exception as e:
                print(f"設定ファイルの読み込みエラー: {e}")
        return {}
    
    def save_config(self):
        """設定ファイルを保存する"""
        try:
            with open(CONFIG_FILE, "w") as f:
                json.dump(self.config, f)
        except Exception as e:
            print(f"設定ファイルの保存エラー: {e}")
            messagebox.showerror("エラー", f"設定の保存に失敗しました: {e}")

def main():
    """メイン関数"""
    try:
        root = tk.Tk()
        app = TranscribeApp(root)
        root.mainloop()
    except Exception as e:
        print(f"GUIの起動に失敗しました: {e}")
        print("コマンドライン版を使用してください: python transcribe_cli.py --help")

if __name__ == "__main__":
    main() 