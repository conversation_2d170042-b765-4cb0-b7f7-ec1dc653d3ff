#!/usr/bin/env python3
"""
MacでFFmpegが利用可能かチェックするスクリプト
"""

import subprocess
import sys
import os

def check_ffmpeg():
    """FFmpegが利用可能かチェック"""
    try:
        # システムのFFmpegをチェック
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ システムのFFmpegが利用可能です")
            print(f"バージョン情報: {result.stdout.split('ffmpeg version')[1].split('\n')[0] if 'ffmpeg version' in result.stdout else '不明'}")
            return True
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    print("✗ システムのFFmpegが見つかりません")
    return False

def install_ffmpeg():
    """FFmpegをインストール"""
    print("\nFFmpegをインストールします...")
    
    # Homebrewがインストールされているかチェック
    try:
        result = subprocess.run(['brew', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("Homebrewを使用してFFmpegをインストールします...")
            subprocess.run(['brew', 'install', 'ffmpeg'], check=True)
            print("✓ FFmpegのインストールが完了しました。")
            return True
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("Homebrewがインストールされていません。")
    
    print("手動でFFmpegをインストールしてください:")
    print("1. Homebrewをインストール: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
    print("2. FFmpegをインストール: brew install ffmpeg")
    return False

def main():
    """メイン関数"""
    print("=== FFmpegチェック ===")
    
    if check_ffmpeg():
        print("\nFFmpegは正常に動作します。")
        return True
    else:
        print("\nFFmpegをインストールする必要があります。")
        install_ffmpeg()
        return False

if __name__ == "__main__":
    main() 