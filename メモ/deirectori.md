# 音声文字起こしツール - ディレクトリ構造

## プロジェクト概要
Google Gemini APIを使用した音声文字起こし・議事録作成ツールのプロジェクト構造です。

## ディレクトリ構造

```
Transcription/
├── 📁 プロジェクトルート
│   ├── 📄 README.md                    # プロジェクトの説明書
│   ├── 📄 requirements.txt             # Python依存関係ファイル
│   ├── 📄 build.bat                    # Windows用ビルドスクリプト
│   ├── 📄 transcribe_gui.py            # GUIアプリケーションのメインファイル
│   ├── 📄 transcribe.py                # 音声処理・文字起こしのコア機能
│   ├── 📄 app.ico                      # アプリケーションアイコン
│   │
│   ├── 📁 bin/                         # 開発用FFmpegバイナリ
│   │   ├── 📄 ffmpeg.exe              # 音声変換ツール
│   │   ├── 📄 ffplay.exe              # 音声再生ツール
│   │   └── 📄 ffprobe.exe             # 音声情報取得ツール
│   │
│   ├── 📁 env/                         # Python仮想環境
│   │   ├── 📁 bin/                     # 仮想環境の実行ファイル
│   │   ├── 📁 lib/                     # 仮想環境のライブラリ
│   │   ├── 📁 include/                 # 仮想環境のヘッダーファイル
│   │   └── 📄 pyvenv.cfg              # 仮想環境設定ファイル
│   │
│   ├── 📁 release/                     # リリース版（本番用）
│   │   ├── 📄 音声文字起こしツール.exe  # 実行可能ファイル（135MB）
│   │   ├── 📄 config.json              # 設定ファイル
│   │   ├── 📄 README.md                # リリース版説明書
│   │   └── 📁 ffmpeg/                  # リリース用FFmpeg
│   │       └── 📁 bin/
│   │           ├── 📄 ffmpeg.exe       # 音声変換ツール（122MB）
│   │           ├── 📄 ffplay.exe       # 音声再生ツール（121MB）
│   │           └── 📄 ffprobe.exe      # 音声情報取得ツール（121MB）
│   │
│   ├── 📁 release_debug/               # デバッグ版（開発用）
│   │   ├── 📄 音声文字起こしツール.exe  # デバッグ用実行ファイル
│   │   ├── 📄 README.md                # デバッグ版説明書
│   │   └── 📁 ffmpeg/                  # デバッグ用FFmpeg
│   │       └── 📁 bin/
│   │           ├── 📄 ffmpeg.exe       # 音声変換ツール
│   │           ├── 📄 ffplay.exe       # 音声再生ツール
│   │           └── 📄 ffprobe.exe      # 音声情報取得ツール
│   │
│   └── 📁 メモ/                        # プロジェクトメモ
│       └── 📄 deirectori.md           # このファイル（ディレクトリ構造説明）
```

## 各ファイルの詳細説明

### 📄 メインファイル

#### `README.md`
- **役割**: プロジェクトの概要、機能、使用方法、注意事項を説明
- **内容**: 
  - 音声文字起こしツールの機能紹介
  - 使用方法の手順
  - Google API キーの設定方法
  - トラブルシューティング情報

#### `requirements.txt`
- **役割**: Pythonプロジェクトの依存関係を定義
- **内容**:
  - `pydub>=0.25.1` - 音声ファイル処理ライブラリ
  - `google-generativeai>=0.3.0` - Google Gemini APIクライアント
  - `numpy>=1.24.0` - 数値計算ライブラリ
  - `pyinstaller>=6.0.0` - 実行可能ファイル作成ツール

#### `build.bat`
- **役割**: Windows用のビルドスクリプト
- **機能**:
  - 必要なパッケージのインストール
  - FFmpegの自動ダウンロード・設定
  - PyInstallerを使用したexeファイルの作成

### 📄 コアアプリケーションファイル

#### `transcribe_gui.py` (825行)
- **役割**: GUIアプリケーションのメインファイル
- **機能**:
  - TkinterベースのGUIインターフェース
  - ファイル選択機能
  - APIキー設定機能
  - 文字起こし実行機能
  - 結果表示・保存機能
  - 議事録生成機能

#### `transcribe.py` (503行)
- **役割**: 音声処理・文字起こしのコア機能
- **機能**:
  - 音声ファイルの読み込み・変換
  - Google Gemini APIを使用した文字起こし
  - 長い音声ファイルの自動分割処理
  - タイムスタンプ付き文字起こし
  - 議事録の自動生成
  - FFmpegの設定・管理

#### `app.ico`
- **役割**: アプリケーションのアイコンファイル
- **用途**: exeファイルのアイコンとして使用

### 📁 開発環境

#### `bin/` ディレクトリ
- **役割**: 開発用のFFmpegバイナリファイル
- **ファイル**:
  - `ffmpeg.exe` (84MB) - 音声変換ツール
  - `ffplay.exe` (83MB) - 音声再生ツール
  - `ffprobe.exe` (83MB) - 音声情報取得ツール

#### `env/` ディレクトリ
- **役割**: Python仮想環境
- **内容**:
  - `bin/` - 仮想環境の実行ファイル
  - `lib/` - 仮想環境のライブラリ
  - `include/` - 仮想環境のヘッダーファイル
  - `pyvenv.cfg` - 仮想環境設定（Python 3.9.6）

### 📁 リリース版

#### `release/` ディレクトリ
- **役割**: 本番用のリリース版
- **内容**:
  - `音声文字起こしツール.exe` (135MB) - メイン実行ファイル
  - `config.json` - アプリケーション設定ファイル
  - `README.md` - リリース版の説明書
  - `ffmpeg/bin/` - リリース用FFmpegバイナリ（各121-122MB）

#### `release_debug/` ディレクトリ
- **役割**: デバッグ用のリリース版
- **内容**: デバッグ情報付きの実行ファイルとFFmpegバイナリ

### 📁 メモ

#### `メモ/deirectori.md`
- **役割**: このファイル（ディレクトリ構造の説明書）
- **内容**: プロジェクト全体の構造と各ファイルの役割を詳細に説明

## 技術スタック

- **言語**: Python 3.9.6
- **GUI**: Tkinter
- **音声処理**: pydub + FFmpeg
- **AI文字起こし**: Google Gemini API
- **ビルドツール**: PyInstaller
- **OS対応**: Windows（.exeファイル）

## ファイルサイズ情報

- メイン実行ファイル: 135MB
- FFmpegバイナリ（各）: 121-122MB
- 開発用FFmpegバイナリ（各）: 83-84MB
- 合計リリースサイズ: 約500MB

## 開発ワークフロー

1. **開発**: `transcribe_gui.py` と `transcribe.py` を編集
2. **テスト**: 仮想環境（`env/`）でテスト実行
3. **ビルド**: `build.bat` でexeファイルを作成
4. **リリース**: `release/` または `release_debug/` に配置
