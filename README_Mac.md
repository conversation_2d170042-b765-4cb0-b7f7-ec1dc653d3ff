# 音声文字起こしツール - Mac版

このツールは音声ファイルを文字起こしし、議事録を自動生成するアプリケーションです。

## 必要な環境

- macOS 10.14以上
- Python 3.7以上
- FFmpeg

## セットアップ

### 1. 自動セットアップ（推奨）

```bash
# セットアップスクリプトを実行
python setup_mac.py
```

このスクリプトは以下を自動で行います：
- Pythonバージョンの確認
- FFmpegのインストール（Homebrewを使用）
- 必要なPythonパッケージのインストール
- 設定ファイルの作成

### 2. 手動セットアップ

#### FFmpegのインストール

**Homebrewを使用する場合（推奨）：**
```bash
# Homebrewをインストール（まだインストールしていない場合）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# FFmpegをインストール
brew install ffmpeg
```

**手動でダウンロードする場合：**
1. [FFmpeg公式サイト](https://evermeet.cx/ffmpeg/)からMac用のバイナリをダウンロード
2. 解凍して`ffmpeg/bin/`ディレクトリに配置

#### Pythonパッケージのインストール

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. コマンドライン版（推奨）

```bash
# 基本的な使用方法
python transcribe_cli.py 音声ファイル.mp3

# オプション付き
python transcribe_cli.py 音声ファイル.mp3 -o 出力.txt -m 議事録.md

# ヘルプ表示
python transcribe_cli.py --help
```

#### コマンドライン版のオプション

- `-o, --output`: 文字起こし結果の出力ファイル
- `-m, --minutes`: 議事録の出力ファイル
- `--model`: 使用するAIモデル（デフォルト: gemini-2.0-flash）
- `--language`: 言語選択（japanese/english）
- `--no-timestamp`: タイムスタンプを無効にする
- `--no-minutes`: 議事録生成を無効にする
- `--api-key`: APIキーをコマンドラインで指定

### 2. GUI版（実験的）

```bash
python transcribe_gui_simple.py
```

**注意**: GUI版はmacOSのバージョンチェックにより動作しない場合があります。その場合はコマンドライン版を使用してください。

## 対応音声フォーマット

- WAV
- FLAC
- MP3
- OGG
- WebM
- MP4
- AMR
- 3GP
- M4A
- Opus
- Speex

## 使用例

### 基本的な文字起こし
```bash
python transcribe_cli.py sample.mp3
```

### 議事録付きで文字起こし
```bash
python transcribe_cli.py meeting.mp3 -o transcript.txt -m minutes.md
```

### 英語音声の文字起こし
```bash
python transcribe_cli.py english_audio.mp3 --language english
```

### タイムスタンプなしで文字起こし
```bash
python transcribe_cli.py audio.mp3 --no-timestamp
```

## トラブルシューティング

### FFmpegが見つからないエラー

```bash
# FFmpegが正しくインストールされているか確認
ffmpeg -version

# パスが通っているか確認
which ffmpeg
```

### Pythonパッケージのエラー

```bash
# パッケージを再インストール
pip install --upgrade -r requirements.txt
```

### APIキーのエラー

- APIキーが正しく入力されているか確認
- インターネット接続を確認
- [Google AI Studio](https://aistudio.google.com/)でAPIキーが有効か確認

### GUI版が起動しない場合

macOSのバージョンチェックエラーが発生する場合は、コマンドライン版を使用してください：

```bash
python transcribe_cli.py 音声ファイル.mp3
```

## ファイル構成

```
Transcription/
├── transcribe_cli.py        # コマンドライン版（推奨）
├── transcribe_gui_simple.py # シンプルなGUI版
├── transcribe.py            # 文字起こしエンジン
├── setup_mac.py             # Mac用セットアップスクリプト
├── check_ffmpeg_mac.py      # FFmpegチェックスクリプト
├── requirements.txt          # Pythonパッケージ一覧
├── config.json              # 設定ファイル（自動生成）
└── README_Mac.md           # このファイル
```

## 注意事項

- 音声ファイルは25分以内を推奨
- 大きなファイルは処理に時間がかかります
- インターネット接続が必要です
- APIキーは安全に管理してください
- GUI版はmacOSのバージョンによって動作しない場合があります

## ライセンス

このプロジェクトはMITライセンスの下で公開されています。 