#!/usr/bin/env python3
"""
Mac用のFFmpegバイナリをダウンロードするスクリプト
"""

import os
import sys
import urllib.request
import zipfile
import shutil
from pathlib import Path

def download_ffmpeg_mac():
    """Mac用のFFmpegバイナリをダウンロードする"""
    
    # FFmpegのダウンロードURL（Mac用）
    ffmpeg_url = "https://evermeet.cx/ffmpeg/getrelease/zip"
    
    # 作業ディレクトリ
    base_dir = Path(__file__).parent
    ffmpeg_dir = base_dir / "ffmpeg"
    bin_dir = ffmpeg_dir / "bin"
    
    print("Mac用のFFmpegバイナリをダウンロードしています...")
    
    try:
        # ディレクトリを作成
        bin_dir.mkdir(parents=True, exist_ok=True)
        
        # 一時ファイル名
        zip_file = base_dir / "ffmpeg_mac.zip"
        
        # ダウンロード
        print(f"FFmpegをダウンロード中: {ffmpeg_url}")
        urllib.request.urlretrieve(ffmpeg_url, zip_file)
        
        # 解凍
        print("FFmpegを解凍中...")
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall(base_dir)
        
        # 解凍されたファイルを移動
        extracted_dir = base_dir / "ffmpeg"
        if extracted_dir.exists():
            # 既存のffmpegディレクトリを削除
            if ffmpeg_dir.exists():
                shutil.rmtree(ffmpeg_dir)
            
            # 解凍されたファイルを移動
            shutil.move(str(extracted_dir), str(ffmpeg_dir))
        
        # 実行権限を付与
        ffmpeg_bin = bin_dir / "ffmpeg"
        ffprobe_bin = bin_dir / "ffprobe"
        
        if ffmpeg_bin.exists():
            os.chmod(ffmpeg_bin, 0o755)
        if ffprobe_bin.exists():
            os.chmod(ffprobe_bin, 0o755)
        
        # 一時ファイルを削除
        if zip_file.exists():
            zip_file.unlink()
        
        print("FFmpegのダウンロードが完了しました！")
        print(f"FFmpegバイナリの場所: {bin_dir}")
        
    except Exception as e:
        print(f"エラーが発生しました: {e}")
        print("手動でFFmpegをインストールしてください:")
        print("1. https://evermeet.cx/ffmpeg/ からFFmpegをダウンロード")
        print("2. 解凍してffmpeg/bin/ディレクトリに配置")
        print("3. または、Homebrewを使用: brew install ffmpeg")

if __name__ == "__main__":
    if sys.platform != "darwin":
        print("このスクリプトはMac専用です。")
        sys.exit(1)
    
    download_ffmpeg_mac() 