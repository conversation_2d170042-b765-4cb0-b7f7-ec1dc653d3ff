#!/usr/bin/env python3
"""
コマンドライン版音声文字起こしツール
"""

import os
import sys
import warnings
import argparse
import json
from pathlib import Path

# urllib3の警告を抑制
warnings.filterwarnings("ignore", category=UserWarning, module="urllib3")

# 既存のtranscribe.pyから関数をインポート
from transcribe import load_audio_file, transcribe_audio

# 設定ファイルのパス
CONFIG_FILE = "config.json"

def load_config():
    """設定ファイルを読み込む"""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, "r") as f:
                return json.load(f)
        except Exception as e:
            print(f"設定ファイルの読み込みエラー: {e}")
    return {}

def save_config(config):
    """設定ファイルを保存する"""
    try:
        with open(CONFIG_FILE, "w") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"設定ファイルの保存エラー: {e}")

def setup_api_key():
    """APIキーを設定する"""
    config = load_config()
    
    if "api_key" in config and config["api_key"]:
        print(f"現在のAPIキー: {config['api_key'][:10]}...")
        use_current = input("現在のAPIキーを使用しますか？ (y/n): ").lower()
        if use_current == 'y':
            return config["api_key"]
    
    api_key = input("Google API キーを入力してください: ").strip()
    if not api_key:
        print("エラー: APIキーが入力されていません。")
        return None
    
    # 設定を保存
    config["api_key"] = api_key
    save_config(config)
    
    return api_key

def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description="音声文字起こしツール")
    parser.add_argument("input_file", help="入力音声ファイルのパス")
    parser.add_argument("-o", "--output", help="出力ファイルのパス")
    parser.add_argument("-m", "--minutes", help="議事録出力ファイルのパス")
    parser.add_argument("--model", default="gemini-2.0-flash", help="使用するモデル")
    parser.add_argument("--language", default="japanese", choices=["japanese", "english"], help="言語")
    parser.add_argument("--no-timestamp", action="store_true", help="タイムスタンプを無効にする")
    parser.add_argument("--no-minutes", action="store_true", help="議事録生成を無効にする")
    parser.add_argument("--api-key", help="APIキー（コマンドラインで指定）")
    
    args = parser.parse_args()
    
    # 入力ファイルの確認
    if not os.path.exists(args.input_file):
        print(f"エラー: ファイルが見つかりません: {args.input_file}")
        return 1
    
    # APIキーの設定
    api_key = args.api_key
    if not api_key:
        api_key = setup_api_key()
        if not api_key:
            return 1
    
    # 環境変数に設定
    os.environ["GOOGLE_API_KEY"] = api_key
    
    print(f"音声ファイル: {args.input_file}")
    print(f"モデル: {args.model}")
    print(f"言語: {args.language}")
    print(f"タイムスタンプ: {'無効' if args.no_timestamp else '有効'}")
    print(f"議事録生成: {'無効' if args.no_minutes else '有効'}")
    print()
    
    try:
        # 音声ファイルを読み込み
        print("音声ファイルを読み込み中...")
        audio, format_name = load_audio_file(args.input_file)
        
        duration_minutes = len(audio) / (1000 * 60)
        print(f"音声の長さ: {duration_minutes:.1f}分")
        print()
        
        # 文字起こしを実行
        print("文字起こしを実行中...")
        if not args.no_minutes:
            transcription, minutes = transcribe_audio(
                audio,
                model_name=args.model,
                language=args.language,
                with_timestamps=not args.no_timestamp,
                generate_minutes_flag=True
            )
        else:
            transcription = transcribe_audio(
                audio,
                model_name=args.model,
                language=args.language,
                with_timestamps=not args.no_timestamp
            )
            minutes = None
        
        # 結果を表示
        print("\n=== 文字起こし結果 ===")
        print(transcription)
        
        if minutes:
            print("\n=== 議事録 ===")
            print(minutes)
        
        # ファイルに保存
        if args.output:
            with open(args.output, "w", encoding="utf-8") as f:
                f.write(transcription)
            print(f"\n文字起こし結果を保存しました: {args.output}")
        
        if minutes and args.minutes:
            with open(args.minutes, "w", encoding="utf-8") as f:
                f.write(minutes)
            print(f"議事録を保存しました: {args.minutes}")
        
        # デフォルトファイル名で保存（オプションが指定されていない場合）
        if not args.output:
            default_output = Path(args.input_file).with_suffix('.txt')
            with open(default_output, "w", encoding="utf-8") as f:
                f.write(transcription)
            print(f"\n文字起こし結果を保存しました: {default_output}")
        
        if minutes and not args.minutes:
            default_minutes = Path(args.input_file).stem + "_minutes.md"
            default_minutes = Path(args.input_file).parent / default_minutes
            with open(default_minutes, "w", encoding="utf-8") as f:
                f.write(minutes)
            print(f"議事録を保存しました: {default_minutes}")
        
        print("\n処理が完了しました。")
        return 0
        
    except Exception as e:
        print(f"エラーが発生しました: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 