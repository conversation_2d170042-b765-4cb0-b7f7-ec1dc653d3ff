#!/usr/bin/env python3
"""
Webベースの音声文字起こしツール
"""

import os
import sys
import warnings
import json
import threading
import webbrowser
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_file
import tempfile
import time

# urllib3の警告を抑制
warnings.filterwarnings("ignore", category=UserWarning, module="urllib3")

# 既存のtranscribe.pyから関数をインポート
from transcribe import load_audio_file, transcribe_audio

# Flaskアプリケーション
app = Flask(__name__)

# 設定ファイルのパス
CONFIG_FILE = "config.json"

def load_config():
    """設定ファイルを読み込む"""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, "r") as f:
                return json.load(f)
        except Exception as e:
            print(f"設定ファイルの読み込みエラー: {e}")
    return {}

def save_config(config):
    """設定ファイルを保存する"""
    try:
        with open(CONFIG_FILE, "w") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"設定ファイルの保存エラー: {e}")

# HTMLテンプレート
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音声文字起こしツール</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            margin-top: 0;
            color: #555;
        }
        input[type="text"], input[type="file"], select {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            min-height: 200px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        .checkbox-group {
            margin: 10px 0;
        }
        .checkbox-group label {
            display: inline-block;
            margin-right: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>音声・動画文字起こし・議事録作成ツール</h1>
        
        <div class="section">
            <h2>API設定</h2>
            <input type="text" id="apiKey" placeholder="Google API キーを入力してください" value="">
            <button onclick="saveApiKey()">APIキーを保存</button>
            <div id="apiStatus" class="status" style="display: none;"></div>
        </div>
        
        <div class="section">
            <h2>音声・動画ファイルの選択</h2>
            <input type="file" id="audioFile" accept=".wav,.flac,.mp3,.ogg,.webm,.mp4,.amr,.3gp,.m4a,.opus,.speex,.avi,.mov,.mkv">
            <p style="font-size: 12px; color: #666; margin-top: 5px;">
                対応形式: WAV, FLAC, MP3, OGG, WebM, MP4, AVI, MOV, MKV, AMR, 3GP, M4A, Opus, Speex
            </p>
        </div>
        
        <div class="section">
            <h2>オプション設定</h2>
            <div>
                <label>モデル:</label>
                <select id="model">
                    <option value="gemini-2.0-flash">gemini-2.0-flash</option>
                </select>
            </div>
            <div>
                <label>言語:</label>
                <select id="language">
                    <option value="japanese">日本語</option>
                    <option value="english">英語</option>
                </select>
            </div>
            <div class="checkbox-group">
                <label><input type="checkbox" id="timestamp" checked> タイムスタンプを付ける</label>
                <label><input type="checkbox" id="minutes" checked> 議事録も生成する</label>
                <label><input type="checkbox" id="autosave"> 結果を自動的にファイルに保存する</label>
            </div>
        </div>
        
        <div class="section">
            <h2>処理実行</h2>
            <button onclick="executeTranscription()" id="executeBtn">文字起こしを実行</button>
            <div class="progress" id="progress" style="display: none;">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div id="status" class="status" style="display: none;"></div>
        </div>
        
        <div class="section">
            <h2>文字起こし結果</h2>
            <div class="result-area" id="transcriptionResult">結果がここに表示されます...</div>
            <button onclick="saveTranscription()" id="saveTranscriptionBtn" style="display: none;">結果を保存</button>
        </div>
        
        <div class="section">
            <h2>議事録</h2>
            <div class="result-area" id="minutesResult">議事録がここに表示されます...</div>
            <button onclick="saveMinutes()" id="saveMinutesBtn" style="display: none;">議事録を保存</button>
        </div>
    </div>

    <script>
        let currentTranscription = "";
        let currentMinutes = "";
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
        }
        
        function showApiStatus(message, type = 'info') {
            const statusDiv = document.getElementById('apiStatus');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
        }
        
        function saveApiKey() {
            const apiKey = document.getElementById('apiKey').value.trim();
            if (!apiKey) {
                showApiStatus('APIキーが入力されていません。', 'error');
                return;
            }
            
            fetch('/save_api_key', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({api_key: apiKey})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showApiStatus('APIキーが保存されました。', 'success');
                } else {
                    showApiStatus('APIキーの保存に失敗しました: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showApiStatus('エラーが発生しました: ' + error, 'error');
            });
        }
        
        function executeTranscription() {
            const fileInput = document.getElementById('audioFile');
            const apiKey = document.getElementById('apiKey').value.trim();
            
            if (!fileInput.files[0]) {
                showStatus('音声・動画ファイルを選択してください。', 'error');
                return;
            }
            
            if (!apiKey) {
                showStatus('APIキーが設定されていません。', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('audio_file', fileInput.files[0]);
            formData.append('api_key', apiKey);
            formData.append('model', document.getElementById('model').value);
            formData.append('language', document.getElementById('language').value);
            formData.append('timestamp', document.getElementById('timestamp').checked);
            formData.append('minutes', document.getElementById('minutes').checked);
            formData.append('autosave', document.getElementById('autosave').checked);
            
            // UIを更新
            document.getElementById('executeBtn').disabled = true;
            document.getElementById('executeBtn').textContent = '処理中...';
            document.getElementById('progress').style.display = 'block';
            document.getElementById('transcriptionResult').textContent = '音声・動画ファイルを処理中です。しばらくお待ちください...';
            document.getElementById('minutesResult').textContent = '議事録を生成中です。しばらくお待ちください...';
            
            fetch('/transcribe', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('executeBtn').disabled = false;
                document.getElementById('executeBtn').textContent = '文字起こしを実行';
                document.getElementById('progress').style.display = 'none';
                
                if (data.success) {
                    currentTranscription = data.transcription;
                    currentMinutes = data.minutes || '';
                    
                    document.getElementById('transcriptionResult').textContent = data.transcription;
                    document.getElementById('minutesResult').textContent = data.minutes || '議事録は生成されませんでした。';
                    
                    if (data.transcription) {
                        document.getElementById('saveTranscriptionBtn').style.display = 'inline-block';
                    }
                    if (data.minutes) {
                        document.getElementById('saveMinutesBtn').style.display = 'inline-block';
                    }
                    
                    showStatus('処理が完了しました。', 'success');
                } else {
                    document.getElementById('transcriptionResult').textContent = 'エラーが発生しました。';
                    document.getElementById('minutesResult').textContent = 'エラーが発生しました。';
                    showStatus('エラーが発生しました: ' + data.error, 'error');
                }
            })
            .catch(error => {
                document.getElementById('executeBtn').disabled = false;
                document.getElementById('executeBtn').textContent = '文字起こしを実行';
                document.getElementById('progress').style.display = 'none';
                document.getElementById('transcriptionResult').textContent = 'エラーが発生しました。';
                document.getElementById('minutesResult').textContent = 'エラーが発生しました。';
                showStatus('エラーが発生しました: ' + error, 'error');
            });
        }
        
        function saveTranscription() {
            if (!currentTranscription) return;
            
            const blob = new Blob([currentTranscription], {type: 'text/plain'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'transcription.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function saveMinutes() {
            if (!currentMinutes) return;
            
            const blob = new Blob([currentMinutes], {type: 'text/markdown'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'minutes.md';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        // 初期化時にAPIキーを読み込み
        window.onload = function() {
            fetch('/get_api_key')
            .then(response => response.json())
            .then(data => {
                if (data.api_key) {
                    document.getElementById('apiKey').value = data.api_key;
                }
            });
        };
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return HTML_TEMPLATE

@app.route('/save_api_key', methods=['POST'])
def save_api_key():
    try:
        data = request.get_json()
        api_key = data.get('api_key', '').strip()
        
        if not api_key:
            return jsonify({'success': False, 'error': 'APIキーが入力されていません。'})
        
        config = load_config()
        config['api_key'] = api_key
        save_config(config)
        
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/get_api_key')
def get_api_key():
    config = load_config()
    return jsonify({'api_key': config.get('api_key', '')})

@app.route('/transcribe', methods=['POST'])
def transcribe():
    try:
        # ファイルの確認
        if 'audio_file' not in request.files:
            return jsonify({'success': False, 'error': '音声・動画ファイルが選択されていません。'})
        
        file = request.files['audio_file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '音声・動画ファイルが選択されていません。'})
        
        # 一時ファイルに保存
        temp_dir = tempfile.mkdtemp()
        temp_file = os.path.join(temp_dir, file.filename)
        file.save(temp_file)
        
        # パラメータの取得
        api_key = request.form.get('api_key', '').strip()
        model = request.form.get('model', 'gemini-2.0-flash')
        language = request.form.get('language', 'japanese')
        with_timestamps = request.form.get('timestamp', 'false').lower() == 'true'
        generate_minutes = request.form.get('minutes', 'false').lower() == 'true'
        autosave = request.form.get('autosave', 'false').lower() == 'true'
        
        if not api_key:
            return jsonify({'success': False, 'error': 'APIキーが設定されていません。'})
        
        # 環境変数に設定
        os.environ["GOOGLE_API_KEY"] = api_key
        
        # 音声・動画ファイルを読み込み
        audio, format_name = load_audio_file(temp_file)
        
        # 文字起こしを実行
        if generate_minutes:
            transcription, minutes = transcribe_audio(
                audio,
                model_name=model,
                language=language,
                with_timestamps=with_timestamps,
                generate_minutes_flag=True
            )
        else:
            transcription = transcribe_audio(
                audio,
                model_name=model,
                language=language,
                with_timestamps=with_timestamps
            )
            minutes = None
        
        # 自動保存の処理
        if autosave:
            output_filepath = Path(temp_file).with_suffix('.txt')
            with open(output_filepath, "w", encoding="utf-8") as f:
                f.write(transcription)
            
            if minutes:
                minutes_filepath = Path(temp_file).stem + "_minutes.md"
                minutes_filepath = Path(temp_file).parent / minutes_filepath
                with open(minutes_filepath, "w", encoding="utf-8") as f:
                    f.write(minutes)
        
        # 一時ファイルを削除
        os.remove(temp_file)
        os.rmdir(temp_dir)
        
        return jsonify({
            'success': True,
            'transcription': transcription,
            'minutes': minutes
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def main():
    """メイン関数"""
    print("=== Web版音声文字起こしツール ===")
    print("ブラウザで http://127.0.0.1:8080 にアクセスしてください。")
    print("Ctrl+C で終了します。")
    
    # ブラウザを自動で開く
    try:
        webbrowser.open('http://127.0.0.1:8080')
    except:
        pass
    
    # Flaskアプリケーションを起動
    app.run(host='127.0.0.1', port=8080, debug=False)

if __name__ == "__main__":
    main() 